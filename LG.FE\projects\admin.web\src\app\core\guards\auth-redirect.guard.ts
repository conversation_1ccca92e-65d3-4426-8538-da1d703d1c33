import { inject } from "@angular/core";
import { CanActivateFn, ActivatedRouteSnapshot, RouterStateSnapshot, Router, UrlTree } from "@angular/router";
import { Observable } from "rxjs";
import { AuthStateService } from "SharedModules.Library";

export const AuthRedirectGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot
):
  Observable<boolean | UrlTree>
  | Promise<boolean | UrlTree>
  | boolean
  | UrlTree => {

  const router = inject(Router);
  const authService = inject(AuthStateService);
  const isAuth = authService.getUserClaims();

  return isAuth
    ? router.createUrlTree(['/dashboard/overview'])
    : true;
};