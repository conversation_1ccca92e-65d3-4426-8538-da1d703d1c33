<!-- Account Switch Dialog Component -->
<app-custom-dialog-popup [maximizable]="false" [styleClass]="'account-switch-dialog-container p-dialog p-dialog-md purple-dialog-header'"
 [(visible)]="visible" (onHide)="onDialogHide()">


 <ng-container dialogHeader>
  <div
  class=" w-full inline-flex align-items-center text-white font-semibold justify-content-center gap-2 fluid-title">
  Switch Account
</div>
</ng-container>



  <!-- Custom Content -->
    <div class="account-switch-dialog" [class.transitioning]="isTransitioning">
      <!-- Header Section with Galaxy Animation -->
      <div class="switch-header">
        <div class="galaxy-container">
          <div class="galaxy-transition">
            <!-- Parent Galaxy (Left) -->
            <div class="galaxy parent-galaxy">
              <div class="galaxy-core parent-core">
                <i class="pi pi-user-edit"></i>
              </div>
              <div class="galaxy-ring ring-1"></div>
              <div class="galaxy-ring ring-2"></div>
              <div class="galaxy-particles">
                <div class="particle particle-1"></div>
                <div class="particle particle-2"></div>
                <div class="particle particle-3"></div>
              </div>
            </div>

            <!-- Transition Bridge -->
            <div class="galaxy-bridge">
              <div class="bridge-line"></div>
              <div class="bridge-particles">
                <div class="bridge-particle"></div>
                <div class="bridge-particle"></div>
                <div class="bridge-particle"></div>
              </div>
            </div>

            <!-- Student Galaxy (Right) -->
            <div class="galaxy student-galaxy">
              <div class="galaxy-core student-core">
                <i class="pi pi-graduation-cap"></i>
              </div>
              <div class="galaxy-ring ring-1"></div>
              <div class="galaxy-ring ring-2"></div>
              <div class="galaxy-particles">
                <div class="particle particle-1"></div>
                <div class="particle particle-2"></div>
                <div class="particle particle-3"></div>
              </div>
            </div>
          </div>
        </div>

        <h3 class="switch-title">Switch to Student Account</h3>
        <p class="switch-subtitle">Journey into your student's learning galaxy</p>
      </div>

      <!-- Student Info Card -->
      <div class="student-preview-card">
        <div class="student-avatar">
          <div class="avatar-circle">
            <i class="pi pi-user"></i>
          </div>
          <div class="student-badge">
            <i class="pi pi-graduation-cap"></i>
          </div>
        </div>
        <div class="student-details">
          <div class="student-name">{{studentData.firstName}} {{studentData.lastName}}</div>
          <div class="student-role">Student Account</div>
        </div>
      </div>

      <!-- Benefits Section -->
      <!-- <div class="switch-benefits">
        <div class="benefit-item">
          <i class="pi pi-eye"></i>
          <span>View lessons & progress</span>
        </div>
        <div class="benefit-item">
          <i class="pi pi-book"></i>
          <span>Access learning materials</span>
        </div>
        <div class="benefit-item">
          <i class="pi pi-cog"></i>
          <span>Test student experience</span>
        </div>
      </div> -->

      <!-- Return Info -->
      <div class="return-info">
        <div class="return-card">
          <i class="pi pi-info-circle"></i>
          <div class="return-text">
            <span class="return-title">Easy Return</span>
            <span class="return-desc">Switch back to parent view anytime from the user menu</span>
          </div>
        </div>
      </div>
    </div>

    <ng-container dialogFooter>
      <div class="flex flex-column sm:flex-row align-items-center justify-content-end gap-2 w-full">
        <p-button 
          label="Stay as Parent" 
          icon="pi pi-times"
          (click)="onCancel()"
          styleClass="p-button-text p-button-secondary">
        </p-button>
        <p-button 
          label="Switch to Student" 
          icon="pi pi-sign-in"
          (click)="onConfirm()"
          styleClass="p-button-primary">
        </p-button>
      </div>
    </ng-container>
</app-custom-dialog-popup>


