import { Injectable, inject } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse, HttpInterceptorFn, HttpEventType, HttpClient, HttpResponse } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, filter, map, mergeMap, switchMap, take, tap } from 'rxjs/operators';
import { IApiResponse, IApiResponseBase, IHttpStatusCode, IRefreshTokenResponse, IdentityRoutes, LocationDataRoutes } from 'SharedModules.Library';

import { Router } from '@angular/router';
import { AuthStateService, EnvironmentService, HandleApiResponseService, ToastService, TokenService } from 'SharedModules.Library';

let isRefreshing = false;
let refreshTokenSubject: BehaviorSubject<string | null> = new BehaviorSubject<string | null>(null);
export const httpErrorInterceptor: HttpInterceptorFn = (req, next) => {

  const toastService = inject(ToastService);
  const router = inject(Router);
  const token = inject(TokenService).getToken();
  const refreshToken = inject(TokenService).getRefreshToken();
  let interceptedReq: HttpRequest<IApiResponse<any>>; // Store the intercepted request
  const handleApiService = inject(HandleApiResponseService);
  const authStateService = inject(AuthStateService);
  const environmentService = inject(EnvironmentService);
  const tokenService = inject(TokenService);
  const httpClient = inject(HttpClient);

  return next(attachAuthorizationHeader(req)).pipe(
    tap((event: HttpEvent<unknown>) => {
      if (event instanceof HttpRequest) {
        interceptedReq = event;
      }
    }),
    map((event: HttpEvent<unknown>) => {
      if (event.type === HttpEventType.Response) {
        const body = (event as HttpResponse<IApiResponse<unknown>>).body;
        if (body && body.messages) {
          const formErrors = body.messages;
          const errorMessages = Object.values(formErrors).flat();
          let concatenatedErrorMessage = errorMessages.join('<br>');
          setTimeout(() => {
            // toastService.show({
            //   summary: 'Message',
            //   detail: concatenatedErrorMessage,
            //   severity: 'info'
            // });
          }, 100);
        }
      }
      return event;

    }),
    catchError((error: HttpErrorResponse) => {
      console.log(error);
      if ((error.error || error.status === IHttpStatusCode.Unauthorized)) {

        if (error.url && error.url.includes(IdentityRoutes.postRefreshToken)) {
          // isRefreshing = false;
          authStateService.logout();
          router.navigate(['/auth/login']);
          // alert('Session expired. Please login again.');
        }

        interceptedReq = req.clone() as HttpRequest<IApiResponse<any>>; // Store the intercepted request here with correct type
        let concatenatedErrorMessage = '';
        const errorMessage = 'An unknown error occurred.';

        if (error.error && error.error.formValidationErrors) {
          const formErrors = error.error.formValidationErrors;
          if (formErrors) {
            const errorMessages = Object.values(formErrors).flat();

            concatenatedErrorMessage += errorMessages.join('<br>');
          }
        }

        switch (error.status) {
          case IHttpStatusCode.Unauthorized:
            if (!isRefreshing) {
              isRefreshing = true;
              refreshTokenSubject.next(null);

              return handleApiService.getApiData<any>(
                {
                  url: IdentityRoutes.postRefreshToken,
                method: 'POST',
                },
                {
                  token : authStateService.getToken(),
                  refreshToken : authStateService.getRefreshToken()
                }
              )
                .pipe(
                  switchMap((tokens: IRefreshTokenResponse) => {
                    isRefreshing = false;
                    refreshTokenSubject.next(tokens.token);
                    // Refresh successful, update request with new access token
                    authStateService.setRefreshToken(tokens.refreshToken);
                    authStateService.setToken(tokens.token);
                    console.log('Refreshed token: ', tokens.token);
                    console.log('interceptedReq: ', interceptedReq);
                    const updatedReq = interceptedReq.clone({ setHeaders: { Authorization: `Bearer ${tokens.token}` } });
                    // Retry the request with the new token
                    return next((updatedReq));
                  }),
                  catchError((refreshError: IApiResponseBase) => {
                    isRefreshing = false;
                    refreshTokenSubject.next(null);
                    console.log(refreshError);
                    // Refresh token failed, handle error (e.g., logout)
                    if (refreshError.statusCode === IHttpStatusCode.Unauthorized || refreshError.statusCode === IHttpStatusCode.Forbidden || refreshError.statusCode === IHttpStatusCode.BadRequest || refreshError.statusCode === IHttpStatusCode.NotFound || refreshError.statusCode === IHttpStatusCode.InternalServerError) {
                      //TODO: add Logout logic
                      authStateService.logout();
                      router.navigate(['/auth/login']);
                      console.error('Refresh token failed:', refreshError);
                      return throwError(() => error);
                    } else {
                      return throwError(() => error);
                    }
                  })
                );
            } else {
              return refreshTokenSubject.pipe(
                filter(token => token !== null),
                switchMap((newToken) => {
                  const updatedReq = interceptedReq.clone({ setHeaders: { Authorization: `Bearer ${newToken}` } });
                  return next(updatedReq);
                })
              );
            }
          case IHttpStatusCode.Forbidden: // Forbidden
            console.error('Forbidden');
            break;
          case IHttpStatusCode.BadRequest:
          case IHttpStatusCode.NotFound:
            console.error('Bad request');
            // Display a toast message using injected ToastService (assuming a provider)
            setTimeout(() => {

              // processErrorMessages(error, toastService);
            }, 100);
            break;
          default:
            //  toastService.show({
            //   summary: 'Message',
            //   detail: error.statusText,
            //   severity: 'info'
            // });
            console.error('Error occurred:', error);
        }

        return throwError(() => error);
      } else {
        console.error(error);
        if (error.status === IHttpStatusCode.Forbidden) {
          toastService.show({
            summary: 'Message',
            detail: 'Forbidden action',
            severity: 'warn'
          });
        }
        return throwError(() => error);
      }
    })
  );
};

function processErrorMessages(error: HttpErrorResponse, toastService: ToastService): void {
  if (error.error?.messages) {
    const formErrors = error.error.messages;
    const errorMessages = Object.values(formErrors).flat();
    let concatenatedErrorMessage = errorMessages.join('<br>');
    concatenatedErrorMessage = concatenatedErrorMessage.replace(/<br>/g, '\n');

    toastService.show({
      summary: 'Message',
      detail: concatenatedErrorMessage,
      severity: 'error'
    });
  }
}

function attachAuthorizationHeader(request: HttpRequest<unknown>): HttpRequest<unknown> {
  const requiresAuth = !isRouteUnprotected(request.url);
  console.log('Requires Auth:', requiresAuth);
  const authService = inject(AuthStateService);
  const token = requiresAuth ? authService.getToken() || null : null;
  
  if (token) {
    return request.clone({
      headers: request.headers.set('Authorization', `Bearer ${token}`)
    });
  }
  return request;
}

// Implement logic to check if the URL requires authorization based on your routing configuration
function isRouteUnprotected(url: string): boolean {
  const locationDataRouteValues = Object.values(LocationDataRoutes).map(route => route as string); // Cast to string
  const protectedRoutes = [
    ...locationDataRouteValues,
    IdentityRoutes.postRegisterParent,
    IdentityRoutes.postLogin,
  ];

  return protectedRoutes.some(route => url.includes(route));
}
