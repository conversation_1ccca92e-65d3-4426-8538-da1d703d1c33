import { Directive, ElementRef, HostListener, Input, inject, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { IntelligentPreloadingStrategy } from '../routes/intelligent-preload-strategy';
import { Subject, timer } from 'rxjs';
import { takeUntil, debounceTime } from 'rxjs/operators';

/**
 * Directive that triggers intelligent preloading on hover events
 * Usage: <a routerLink="/dashboard" appHoverPreload>Dashboard</a>
 */
@Directive({
  selector: '[appHoverPreload]',
  standalone: true
})
export class HoverPreloadDirective implements OnInit, OnDestroy {
  @Input() hoverDelay = 100; // Delay before triggering preload (ms)
  @Input() preloadPriority: 'low' | 'medium' | 'high' = 'medium';
  @Input() enableTouchPreload = false; // Enable preload on touch devices

  private elementRef = inject(ElementRef);
  private router = inject(Router);
  private preloadStrategy = inject(IntelligentPreloadingStrategy);
  private destroy$ = new Subject<void>();
  private hoverSubject = new Subject<void>();
  
  private isPreloaded = false;
  private isTouchDevice = false;

  ngOnInit() {
    this.detectTouchDevice();
    this.setupHoverPreloading();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  @HostListener('mouseenter')
  onMouseEnter() {
    if (this.isTouchDevice && !this.enableTouchPreload) return;
    this.hoverSubject.next();
  }

  @HostListener('mouseleave')
  onMouseLeave() {
    // Cancel pending preload if user moves away quickly
    this.hoverSubject.next();
  }

  @HostListener('touchstart', ['$event'])
  onTouchStart(event: TouchEvent) {
    if (!this.enableTouchPreload) return;
    
    // Preload on touch start for mobile devices
    this.triggerPreload();
    
    // Prevent accidental navigation on long press
    timer(500).pipe(
      takeUntil(this.destroy$)
    ).subscribe(() => {
      event.preventDefault();
    });
  }

  @HostListener('click', ['$event'])
  onClick(event: MouseEvent) {
    const link = this.elementRef.nativeElement;
    const href = link.getAttribute('routerLink') || link.getAttribute('href');
    
    if (href) {
      // Record the click for behavior analysis
      this.preloadStrategy.triggerHoverPreload(href);
      
      // If already preloaded, navigation should be instant
      if (this.isPreloaded) {
        console.log(`⚡ Instant navigation to preloaded route: ${href}`);
      }
    }
  }

  private setupHoverPreloading() {
    this.hoverSubject.pipe(
      debounceTime(this.hoverDelay),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.triggerPreload();
    });
  }

  private triggerPreload() {
    if (this.isPreloaded) return;

    const link = this.elementRef.nativeElement;
    const routerLink = link.getAttribute('routerLink');
    const href = link.getAttribute('href');
    
    const targetRoute = routerLink || href;
    if (!targetRoute) return;

    // Check if route should be preloaded based on current conditions
    if (this.shouldPreload(targetRoute)) {
      this.performPreload(targetRoute);
    }
  }

  private shouldPreload(route: string): boolean {
    // Don't preload external links
    if (route.startsWith('http') || route.startsWith('mailto')) {
      return false;
    }

    // Don't preload auth routes
    if (route.includes('/auth') || route.includes('/login')) {
      return false;
    }

    // Check network conditions
    const connection = (navigator as any).connection;
    if (connection?.saveData) {
      return false;
    }

    // Check if user is on slow connection
    if (connection?.effectiveType === 'slow-2g' || connection?.effectiveType === '2g') {
      return false;
    }

    return true;
  }

  private performPreload(route: string) {
    try {
      // Use Angular's router to preload the route
      this.router.navigate([route], { skipLocationChange: true, replaceUrl: false })
        .then(() => {
          this.isPreloaded = true;
          console.log(`🎯 Hover preloaded: ${route}`);
          
          // Mark as preloaded in the strategy
          this.preloadStrategy.triggerHoverPreload(route);
        })
        .catch(error => {
          console.warn(`Failed to preload route ${route}:`, error);
        });
    } catch (error) {
      console.warn(`Error during hover preload for ${route}:`, error);
    }
  }

  private detectTouchDevice() {
    this.isTouchDevice = 'ontouchstart' in window || 
                       navigator.maxTouchPoints > 0 || 
                       (navigator as any).msMaxTouchPoints > 0;
  }
}

/**
 * Enhanced hover preload directive with intersection observer for viewport-based preloading
 */
@Directive({
  selector: '[appSmartPreload]',
  standalone: true
})
export class SmartPreloadDirective implements OnInit, OnDestroy {
  @Input() preloadThreshold = 0.1; // Preload when 10% visible
  @Input() preloadMargin = '100px'; // Preload 100px before entering viewport

  private elementRef = inject(ElementRef);
  private router = inject(Router);
  private preloadStrategy = inject(IntelligentPreloadingStrategy);
  private observer?: IntersectionObserver;
  private isPreloaded = false;

  ngOnInit() {
    this.setupIntersectionObserver();
  }

  ngOnDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }

  private setupIntersectionObserver() {
    if (!('IntersectionObserver' in window)) {
      // Fallback for browsers without IntersectionObserver
      return;
    }

    const options: IntersectionObserverInit = {
      threshold: this.preloadThreshold,
      rootMargin: this.preloadMargin
    };

    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !this.isPreloaded) {
          this.triggerViewportPreload();
        }
      });
    }, options);

    this.observer.observe(this.elementRef.nativeElement);
  }

  private triggerViewportPreload() {
    const link = this.elementRef.nativeElement;
    const routerLink = link.getAttribute('routerLink');
    const href = link.getAttribute('href');
    
    const targetRoute = routerLink || href;
    if (!targetRoute || this.isPreloaded) return;

    // Use the intelligent preloading strategy
    this.preloadStrategy.triggerHoverPreload(targetRoute);
    this.isPreloaded = true;
    
    console.log(`👁️ Viewport preloaded: ${targetRoute}`);
  }
}

/**
 * Predictive preload directive that uses ML predictions
 */
@Directive({
  selector: '[appPredictivePreload]',
  standalone: true
})
export class PredictivePreloadDirective implements OnInit, OnDestroy {
  @Input() predictionThreshold = 0.7; // Preload if confidence > 70%
  @Input() checkInterval = 5000; // Check predictions every 5 seconds

  private elementRef = inject(ElementRef);
  private preloadStrategy = inject(IntelligentPreloadingStrategy);
  private intervalId?: number;
  private isPreloaded = false;

  ngOnInit() {
    this.startPredictiveChecking();
  }

  ngOnDestroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  private startPredictiveChecking() {
    this.intervalId = window.setInterval(() => {
      this.checkPredictions();
    }, this.checkInterval);

    // Initial check
    this.checkPredictions();
  }

  private checkPredictions() {
    if (this.isPreloaded) return;

    const link = this.elementRef.nativeElement;
    const routerLink = link.getAttribute('routerLink');
    const href = link.getAttribute('href');
    
    const targetRoute = routerLink || href;
    if (!targetRoute) return;

    const stats = this.preloadStrategy.getAdvancedStats();
    const prediction = stats.topPredictions.find(p => p.route === targetRoute);

    if (prediction && prediction.confidence >= this.predictionThreshold) {
      this.preloadStrategy.triggerHoverPreload(targetRoute);
      this.isPreloaded = true;
      
      console.log(`🔮 Predictively preloaded: ${targetRoute} (confidence: ${(prediction.confidence * 100).toFixed(1)}%)`);
    }
  }
}
