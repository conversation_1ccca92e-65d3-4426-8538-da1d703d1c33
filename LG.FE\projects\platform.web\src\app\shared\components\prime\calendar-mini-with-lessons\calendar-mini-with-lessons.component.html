<div class="calendar-with-lessons">
    <div class="" [ngClass]="{'flex-column': !isSideBySideView(), 'grid flex-row justify-content-center': isSideBySideView()}">
        <div class="calendar-card card" [ngClass]="{'col-12': !isSideBySideView(), 'xl:col-5': isSideBySideView()}">
            <app-calendar-agenda 
                (dayClicked)="onDayClicked($event)"
                (calendarLessons)="onLessonsLoaded($event)" 
                [userIdsContext]="userIdsContext">
            </app-calendar-agenda>
        </div>

        <div class="" [ngClass]="{'': !isSideBySideView(), 'xl:col-7': isSideBySideView(), '': !isSideBySideView()}">
            <!-- Lessons Section -->
            @if (hasSelectedDate()) {
                <h3 class="primary-purple-color text-base mt-0 mb-3 px-2">Lessons on {{formatDate(selectedDate())}}</h3>
                <div class="lessons-container">
                    @for (lesson of calendarEvents(); track lesson.lesson?.lessonId!) {
                        @if(lesson.lesson) {
                            <div class="mb-2 px-1">
                                <app-lesson-list-view-card [mini]="false" [lesson]="lesson" />
                            </div>
                        }
                    } @empty {
                        <div class="mt-4">
                            <app-empty-data-image-text 
                                [imageCssClass]="'w-4rem'" 
                                [emptyDataText]="'No lessons for selected date.'"
                                [emptyDataImage]="'/assets/images/graphic/school_search_icon.svg'"
                                [titleCssClass]="'text-700 font-semibold text-center text-sm m-0'">
                            </app-empty-data-image-text>
                        </div>
                    }
                </div>
            } @else {
                <div class="mt-4">
                    <app-empty-data-image-text 
                        [imageCssClass]="'w-4rem'" 
                        [emptyDataText]="'Please select a date to see lessons.'"
                        [emptyDataImage]="'/assets/images/graphic/school_search_icon.svg'"
                        [titleCssClass]="'text-700 font-semibold text-center text-sm m-0'">
                    </app-empty-data-image-text>
                </div>
            }
        </div>
    </div>
</div>