@use "mixins";

// PrimeNG Dialog Styling for Account Switch
::ng-deep {
  .account-switch-dialog-container.p-dialog {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: none;
    overflow: hidden;

    .p-dialog-content {
      padding: 0 !important;
      
      .account-switch-dialog {
        padding: 1.5rem;

        @include mixins.breakpoint(mobile) {
          padding: 1.25rem;
        }
      }

      .switch-header {
        text-align: center;
        margin-bottom: 1.5rem;

        @include mixins.breakpoint(mobile) {
          margin-bottom: 1.25rem;
        }
      }

      // Galaxy Container
      .galaxy-container {
        display: flex;
        justify-content: center;
        margin-bottom: 1.5rem;

        @include mixins.breakpoint(mobile) {
          margin-bottom: 1.25rem;
        }
      }

      .galaxy-transition {
        display: flex;
        align-items: center;
        gap: 2rem;
        position: relative;

        @include mixins.breakpoint(mobile) {
          gap: 1.5rem;
        }
      }

      // Galaxy Base Styles
      .galaxy {
        position: relative;
        width: 64px;
        height: 64px;

        @include mixins.breakpoint(mobile) {
          width: 56px;
          height: 56px;
        }
      }

      .galaxy-core {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1rem;
        z-index: 3;
        transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);

        @include mixins.breakpoint(mobile) {
          width: 28px;
          height: 28px;
          font-size: 0.875rem;
        }
      }

      .parent-core {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        box-shadow: 0 4px 16px rgba(99, 102, 241, 0.4);
      }

      .student-core {
        background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
        box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);
      }

      .galaxy-ring {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: 50%;
        border: 1px solid;
        opacity: 0.3;

        &.ring-1 {
          width: 48px;
          height: 48px;
          animation: galaxy-ring-rotate 8s linear infinite;

          @include mixins.breakpoint(mobile) {
            width: 42px;
            height: 42px;
          }
        }

        &.ring-2 {
          width: 60px;
          height: 60px;
          animation: galaxy-ring-rotate 12s linear infinite reverse;

          @include mixins.breakpoint(mobile) {
            width: 52px;
            height: 52px;
          }
        }
      }

      .parent-galaxy .galaxy-ring {
        border-color: rgba(99, 102, 241, 0.4);
      }

      .student-galaxy .galaxy-ring {
        border-color: rgba(16, 185, 129, 0.4);
      }

      .galaxy-particles {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        height: 100%;

        .particle {
          position: absolute;
          width: 3px;
          height: 3px;
          border-radius: 50%;
          opacity: 0.6;

          @include mixins.breakpoint(mobile) {
            width: 2.5px;
            height: 2.5px;
          }

          &.particle-1 {
            top: 8px;
            left: 50%;
            transform: translateX(-50%);
            animation: particle-float 3s ease-in-out infinite;
          }

          &.particle-2 {
            bottom: 8px;
            right: 12px;
            animation: particle-float 3s ease-in-out infinite 1s;
          }

          &.particle-3 {
            bottom: 8px;
            left: 12px;
            animation: particle-float 3s ease-in-out infinite 2s;
          }
        }
      }

      .parent-galaxy .particle {
        background: rgba(99, 102, 241, 0.8);
        box-shadow: 0 0 6px rgba(99, 102, 241, 0.6);
      }

      .student-galaxy .particle {
        background: rgba(16, 185, 129, 0.8);
        box-shadow: 0 0 6px rgba(16, 185, 129, 0.6);
      }

      // Galaxy Bridge
      .galaxy-bridge {
        position: relative;
        width: 40px;
        height: 2px;

        @include mixins.breakpoint(mobile) {
          width: 32px;
        }

        .bridge-line {
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg,
            rgba(99, 102, 241, 0.6) 0%,
            rgba(139, 92, 246, 0.8) 50%,
            rgba(16, 185, 129, 0.6) 100%);
          border-radius: 1px;
          opacity: 0.7;
          animation: bridge-pulse 2s ease-in-out infinite;
        }

        .bridge-particles {
          position: absolute;
          top: 50%;
          left: 0;
          width: 100%;
          height: 1px;
          transform: translateY(-50%);

          .bridge-particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(139, 92, 246, 0.9);
            border-radius: 50%;
            top: 50%;
            transform: translateY(-50%);
            animation: bridge-particle-flow 2s linear infinite;

            @include mixins.breakpoint(mobile) {
              width: 1.5px;
              height: 1.5px;
            }

            &:nth-child(1) {
              animation-delay: 0s;
            }

            &:nth-child(2) {
              animation-delay: 0.7s;
            }

            &:nth-child(3) {
              animation-delay: 1.4s;
            }
          }
        }
      }

      // Transition State Styles
      &.transitioning {
        .parent-galaxy {
          .galaxy-core {
            transform: translate(-50%, -50%) scale(1.2);
            box-shadow: 0 8px 24px rgba(99, 102, 241, 0.6);
          }

          .galaxy-ring {
            animation-duration: 2s;
            border-color: rgba(99, 102, 241, 0.8);
          }

          .particle {
            animation-duration: 1s;
            opacity: 1;
          }
        }

        .student-galaxy {
          .galaxy-core {
            transform: translate(-50%, -50%) scale(1.3);
            box-shadow: 0 8px 24px rgba(16, 185, 129, 0.6);
          }

          .galaxy-ring {
            animation-duration: 1.5s;
            border-color: rgba(16, 185, 129, 0.8);
          }

          .particle {
            animation-duration: 0.8s;
            opacity: 1;
          }
        }

        .bridge-line {
          animation-duration: 0.5s;
          opacity: 1;
          background: linear-gradient(90deg,
            rgba(99, 102, 241, 0.9) 0%,
            rgba(139, 92, 246, 1) 50%,
            rgba(16, 185, 129, 0.9) 100%);
        }

        .bridge-particle {
          animation-duration: 0.8s;
          opacity: 1;
        }

        .student-preview-card {
          transform: scale(1.02);
          border-color: #10b981;
          box-shadow: 0 8px 24px rgba(16, 185, 129, 0.2);
        }
      }

      .switch-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #111827;
        margin: 0 0 0.5rem;
        line-height: 1.2;
        transition: all 0.3s ease;

        @include mixins.breakpoint(mobile) {
          font-size: 1.375rem;
          margin-bottom: 0.375rem;
        }
      }

      .switch-subtitle {
        font-size: 0.875rem;
        color: #6b7280;
        margin: 0;
        line-height: 1.4;
        transition: all 0.3s ease;

        @include mixins.breakpoint(mobile) {
          font-size: 0.8125rem;
        }
      }

      .student-preview-card {
        display: flex;
        align-items: center;
        gap: 1rem;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border: 2px solid #e5e7eb;
        border-radius: 16px;
        padding: 1.25rem;
        margin-bottom: 1.5rem;
        transition: all 0.2s ease;

        @include mixins.breakpoint(mobile) {
          padding: 1rem;
          margin-bottom: 1.25rem;
          gap: 0.875rem;
        }

        &:hover {
          border-color: #6366f1;
          box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
        }
      }

      .student-avatar {
        position: relative;
        flex-shrink: 0;
      }

      .avatar-circle {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
        border-radius: 50%;
        color: white;

        @include mixins.breakpoint(mobile) {
          width: 40px;
          height: 40px;
        }

        i {
          font-size: 1.25rem;

          @include mixins.breakpoint(mobile) {
            font-size: 1.125rem;
          }
        }
      }

      .student-badge {
        position: absolute;
        bottom: -4px;
        right: -4px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        background: #f59e0b;
        border-radius: 50%;
        color: white;
        border: 2px solid white;

        @include mixins.breakpoint(mobile) {
          width: 18px;
          height: 18px;
        }

        i {
          font-size: 0.75rem;

          @include mixins.breakpoint(mobile) {
            font-size: 0.6875rem;
          }
        }
      }

      .student-details {
        flex: 1;
        min-width: 0;
      }

      .student-name {
        font-size: 1.125rem;
        font-weight: 700;
        color: #111827;
        margin-bottom: 0.25rem;
        line-height: 1.2;

        @include mixins.breakpoint(mobile) {
          font-size: 1rem;
        }
      }

      .student-role {
        font-size: 0.875rem;
        color: #6b7280;
        font-weight: 500;

        @include mixins.breakpoint(mobile) {
          font-size: 0.8125rem;
        }
      }

      .switch-benefits {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        margin-bottom: 1.5rem;

        @include mixins.breakpoint(mobile) {
          gap: 0.625rem;
          margin-bottom: 1.25rem;
        }
      }

      .benefit-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: rgba(99, 102, 241, 0.05);
        border-radius: 12px;
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        transition: all 0.2s ease;

        @include mixins.breakpoint(mobile) {
          padding: 0.625rem;
          gap: 0.625rem;
          font-size: 0.8125rem;
        }

        &:hover {
          background: rgba(99, 102, 241, 0.1);
          transform: translateX(4px);
        }

        i {
          color: #6366f1;
          font-size: 1rem;
          width: 16px;
          flex-shrink: 0;

          @include mixins.breakpoint(mobile) {
            font-size: 0.875rem;
            width: 14px;
          }
        }
      }

      .return-info {
        margin-top: 1rem;

        @include mixins.breakpoint(mobile) {
          margin-top: 0.875rem;
        }
      }

      .return-card {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 1rem;
        background: var(--blue-50);
        border: 1px solid var(--blue-300);
        border-radius: 12px;

        @include mixins.breakpoint(mobile) {
          padding: 0.875rem;
          gap: 0.625rem;
        }

        i {
          color: var(--blue-700);
          font-size: 1.125rem;
          margin-top: 0.125rem;
          flex-shrink: 0;

          @include mixins.breakpoint(mobile) {
            font-size: 1rem;
          }
        }
      }

      .return-text {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
      }

      .return-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--blue-700);

        @include mixins.breakpoint(mobile) {
          font-size: 0.8125rem;
        }
      }

      .return-desc {
        font-size: 0.8125rem;
        color: var(--blue-700);
        line-height: 1.4;

        @include mixins.breakpoint(mobile) {
          font-size: 0.75rem;
        }
      }
    }

    .p-dialog-footer {
      padding: 1.25rem 1.5rem;
      border-top: 1px solid #e5e7eb;
      background: #f9fafb;
      border-radius: 0 0 16px 16px;

      @include mixins.breakpoint(mobile) {
        padding: 1rem;
      }

      .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 0.75rem;
        width: 100%;

        @include mixins.breakpoint(mobile) {
          flex-direction: column-reverse;
          gap: 0.5rem;
        }

        .p-button {
          padding: 0.75rem 1.5rem;
          font-weight: 600;
          border-radius: 8px;
          transition: all 0.2s ease;

          @include mixins.breakpoint(mobile) {
            width: 100%;
            justify-content: center;
            padding: 0.875rem 1.5rem;
          }

          &.p-button-text {
            background: transparent;
            color: #6b7280;
            border: 1px solid #d1d5db;

            &:hover {
              background: #f3f4f6;
              color: #374151;
              border-color: #9ca3af;
            }
          }

          &.p-button-primary {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            border: 1px solid #6366f1;
            color: white;

            &:hover {
              background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
              border-color: #4f46e5;
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
            }
          }
        }
      }
    }
  }
}

// Galaxy Animations
@keyframes galaxy-ring-rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes particle-float {
  0%, 100% {
    opacity: 0.4;
    transform: translateY(0px);
  }
  50% {
    opacity: 1;
    transform: translateY(-3px);
  }
}

@keyframes bridge-pulse {
  0%, 100% {
    opacity: 0.5;
    transform: scaleY(1);
  }
  50% {
    opacity: 1;
    transform: scaleY(1.5);
  }
}

@keyframes bridge-particle-flow {
  0% {
    left: 0%;
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

// Reduced Motion Support
@media (prefers-reduced-motion: reduce) {
  .galaxy-transition {
    .galaxy-ring {
      animation: none;
    }

    .particle {
      animation: none;
      opacity: 0.6;
    }

    .bridge-line {
      animation: none;
      opacity: 0.7;
    }

    .bridge-particle {
      animation: none;
      opacity: 0;
    }

    &.transitioning {
      .galaxy-core {
        transform: translate(-50%, -50%) scale(1.1) !important;
        transition: transform 0.3s ease;
      }
    }
  }
}
